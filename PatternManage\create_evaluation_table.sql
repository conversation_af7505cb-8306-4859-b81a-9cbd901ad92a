USE pattern_management;

CREATE TABLE IF NOT EXISTS evaluation (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    pattern_id INT NOT NULL,
    description ENUM('Thành công', 'Không thành công') NOT NULL,
    evaluation_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    -- <PERSON><PERSON><PERSON> bu<PERSON><PERSON> unique: mỗi user chỉ đánh giá 1 lần cho 1 pattern
    UNIQUE KEY unique_user_pattern (user_id, pattern_id)
);

-- Thêm một vài bản ghi test
INSERT INTO evaluation (user_id, pattern_id, description) VALUES
(1, 1, 'Thành công'),
(1, 2, 'Không thành công'),
(2, 1, 'Thành công'),
(2, 3, 'Thành công'),
(3, 1, 'Không thành công'),
(3, 2, 'Thành công');

SELECT 'Evaluation table created successfully!' as message;
