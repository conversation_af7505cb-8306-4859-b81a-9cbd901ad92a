-- Tạ<PERSON> bảng evaluation để lưu trữ đánh giá thành công/không thành công của người dùng
CREATE TABLE IF NOT EXISTS evaluation (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    pattern_id INT NOT NULL,
    description ENUM('Thành công', 'Không thành công') NOT NULL,
    evaluation_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- <PERSON><PERSON><PERSON> buộ<PERSON> unique: mỗi user chỉ đánh giá 1 lần cho 1 pattern
    UNIQUE KEY unique_user_pattern (user_id, pattern_id),
    
    -- Index để tối ưu truy vấn
    INDEX idx_user_id (user_id),
    INDEX idx_pattern_id (pattern_id),
    INDEX idx_evaluation_date (evaluation_date),
    INDEX idx_description (description)
);
