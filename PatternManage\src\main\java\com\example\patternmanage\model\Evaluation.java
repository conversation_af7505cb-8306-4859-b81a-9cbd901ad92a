package com.example.patternmanage.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import javax.persistence.*;
import javax.validation.constraints.NotBlank;
import java.time.LocalDateTime;

/**
 * Model cho bảng evaluation
 * Lưu trữ đánh giá thành công/không thành công của người dùng đối với mẫu
 */
@Entity
@Table(name = "evaluations")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class Evaluation {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private int id;

    @Column(name = "user_id", nullable = false)
    private int userId;

    @Column(name = "pattern_id", nullable = false)
    private int patternId;

    @NotBlank(message = "<PERSON><PERSON> tả đánh giá không được để trống")
    @Column(name = "description", nullable = false)
    private String description; // "Thành công" hoặc "Không thành công"

    @Column(name = "evaluation_date", nullable = false)
    private LocalDateTime evaluationDate;

    @CreationTimestamp
    @Column(name = "created_at", updatable = false)
    private LocalDateTime createdAt;

    @UpdateTimestamp
    @Column(name = "updated_at")
    private LocalDateTime updatedAt;

    // Thông tin bổ sung từ join với bảng khác (không map vào database)
    @Transient
    private String patternName;

    @Transient
    private String username;

    @Transient
    private String userEmail;

    /**
     * Constructor tiện ích cho việc tạo evaluation mới
     */
    public Evaluation(int userId, int patternId, String description) {
        this.userId = userId;
        this.patternId = patternId;
        this.description = description;
        this.evaluationDate = LocalDateTime.now();
    }

    /**
     * Constructor với thời gian tùy chỉnh
     */
    public Evaluation(int userId, int patternId, String description, LocalDateTime evaluationDate) {
        this.userId = userId;
        this.patternId = patternId;
        this.description = description;
        this.evaluationDate = evaluationDate;
    }

    /**
     * Kiểm tra xem đánh giá có thành công không
     */
    public boolean isSuccess() {
        return "Thành công".equals(this.description);
    }

    /**
     * Kiểm tra xem đánh giá có thất bại không
     */
    public boolean isFailed() {
        return "Không thành công".equals(this.description);
    }

    /**
     * Lấy mô tả đánh giá dưới dạng chuỗi tiếng Việt
     */
    public String getDescriptionText() {
        return this.description != null ? this.description : "";
    }

    /**
     * Override toString để hiển thị thông tin đầy đủ
     */
    @Override
    public String toString() {
        return "Evaluation{" +
                "id=" + id +
                ", userId=" + userId +
                ", patternId=" + patternId +
                ", description='" + description + '\'' +
                ", evaluationDate=" + evaluationDate +
                ", username='" + username + '\'' +
                ", patternName='" + patternName + '\'' +
                '}';
    }
}
