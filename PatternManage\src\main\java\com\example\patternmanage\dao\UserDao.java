package com.example.patternmanage.dao;

import com.example.patternmanage.model.User;
import com.example.patternmanage.model.UserRole;
import org.springframework.stereotype.Service;

import javax.persistence.EntityManager;
import javax.persistence.NoResultException;
import javax.persistence.PersistenceContext;
import javax.transaction.Transactional;
import java.time.LocalDateTime;
import java.util.List;

@Service
public class UserDao {

    @PersistenceContext
    private EntityManager entityManager;

    public List<User> findAll() {
        String jpql = "SELECT u FROM User u ORDER BY u.createdAt DESC";
        return entityManager.createQuery(jpql, User.class)
                .getResultList();
    }

    public User findById(int id) {
        return entityManager.find(User.class, id);
    }

    public User findByUsername(String username) {
        try {
            String jpql = "SELECT u FROM User u WHERE u.username = :username";
            return entityManager.createQuery(jpql, User.class)
                    .setParameter("username", username)
                    .getSingleResult();
        } catch (NoResultException e) {
            return null;
        }
    }


    public boolean existsByUsernameOrEmail(String username, String email) {
        String jpql = "SELECT COUNT(u) FROM User u WHERE u.username = :username OR u.email = :email";
        Long count = entityManager.createQuery(jpql, Long.class)
                .setParameter("username", username)
                .setParameter("email", email)
                .getSingleResult();
        return count > 0;
    }

    public boolean existsByUsernameOrEmailExcludingId(String username, String email, int excludeId) {
        String jpql = "SELECT COUNT(u) FROM User u WHERE (u.username = :username OR u.email = :email) AND u.id <> :excludeId";
        Long count = entityManager.createQuery(jpql, Long.class)
                .setParameter("username", username)
                .setParameter("email", email)
                .setParameter("excludeId", excludeId)
                .getSingleResult();
        return count > 0;
    }

    @Transactional
    public void saveOrUpdate(Integer id, String username, String email, String password,
                           String name, String phoneNumber, UserRole role) {
        User user;
        if (id != null) {
            user = entityManager.find(User.class, id);
            if (user == null) {
                throw new RuntimeException("Không tìm thấy người dùng với ID: " + id);
            }
        } else {
            user = new User();
            user.setCreatedAt(LocalDateTime.now());
        }

        user.setUsername(username);
        user.setEmail(email);
        user.setPassword(password);
        user.setName(name);
        user.setPhoneNumber(phoneNumber);
        user.setRole(role != null ? role : UserRole.USER);
        user.setActive(true);

        if (id == null) {
            entityManager.persist(user);
        } else {
            entityManager.merge(user);
        }
    }


    @Transactional
    public void deleteById(int id) {
        User user = entityManager.find(User.class, id);
        if (user != null) {
            entityManager.remove(user);
        }
    }

    public List<User> searchByKeyword(String keyword) {
        String jpql = "SELECT u FROM User u WHERE LOWER(u.username) LIKE LOWER(CONCAT('%', :keyword, '%')) " +
                     "OR LOWER(u.email) LIKE LOWER(CONCAT('%', :keyword, '%')) " +
                     "OR LOWER(u.name) LIKE LOWER(CONCAT('%', :keyword, '%'))";
        return entityManager.createQuery(jpql, User.class)
                .setParameter("keyword", keyword)
                .getResultList();
    }

    public boolean isAdmin(int userId) {
        User user = findById(userId);
        return user != null && user.getRole() == UserRole.ADMIN;
    }

    @Transactional
    public void updateUserStatus(int userId, boolean isActive) {
        User user = entityManager.find(User.class, userId);
        if (user == null) {
            throw new RuntimeException("Không tìm thấy người dùng với ID: " + userId);
        }
        user.setActive(isActive);
        entityManager.merge(user);
    }

    public long countAdmins() {
        String jpql = "SELECT COUNT(u) FROM User u WHERE u.role = :role";
        return entityManager.createQuery(jpql, Long.class)
                .setParameter("role", UserRole.ADMIN)
                .getSingleResult();
    }

    public long countUsers() {
        String jpql = "SELECT COUNT(u) FROM User u WHERE u.role = :role";
        return entityManager.createQuery(jpql, Long.class)
                .setParameter("role", UserRole.USER)
                .getSingleResult();
    }

    public long countAll() {
        String jpql = "SELECT COUNT(u) FROM User u";
        return entityManager.createQuery(jpql, Long.class)
                .getSingleResult();
    }

    public long countActiveUsers() {
        String jpql = "SELECT COUNT(u) FROM User u WHERE u.isActive = true";
        return entityManager.createQuery(jpql, Long.class)
                .getSingleResult();
    }
}
