package com.example.patternmanage.controller;

import com.example.patternmanage.dao.PatternDao;
import com.example.patternmanage.dao.DownloadDao;
import com.example.patternmanage.dao.EvaluationDao;
import com.example.patternmanage.model.Pattern;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.*;

/**
 * Controller xử lý các API thống kê cho admin
 */
@RestController
@RequestMapping("/statistics")
@CrossOrigin(origins = "*")
public class StatisticsController {

    @Autowired
    private PatternDao patternDao;

    @Autowired
    private DownloadDao downloadDao;

    @Autowired
    private EvaluationDao evaluationDao;

    /**
     * L<PERSON>y thống kê tổng quan của hệ thống
     */
    @GetMapping("/overview")
    public ResponseEntity<?> getOverviewStatistics() {
        try {
            Map<String, Object> response = new HashMap<>();

            // Thống kê tổng quan
            Map<String, Object> overview = new HashMap<>();
            overview.put("totalPatterns", patternDao.getTotalPatterns());
            overview.put("totalUsers", patternDao.getTotalUsers());
            overview.put("totalDownloads", downloadDao.getTotalDownloads());
            overview.put("totalEvaluations", evaluationDao.getTotalEvaluations());

            // Thêm thống kê thành công/thất bại
            overview.put("totalSuccessCount", evaluationDao.getTotalSuccessCount());
            overview.put("totalFailureCount", evaluationDao.getTotalFailureCount());

            response.put("success", true);
            response.put("overview", overview);

            return ResponseEntity.ok(response);
        } catch (Exception e) {
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "Lỗi khi lấy thống kê tổng quan: " + e.getMessage());
            return ResponseEntity.status(500).body(response);
        }
    }

    /**
     * Lấy thống kê chi tiết của từng mẫu
     */
    @GetMapping("/patterns")
    public ResponseEntity<?> getPatternsStatistics(
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "5") int size,
            @RequestParam(defaultValue = "usageCount") String sortBy,
            @RequestParam(defaultValue = "desc") String sortOrder) {
        try {
            // Lấy tất cả patterns
            List<Pattern> allPatterns = patternDao.findAll();

            // Tạo danh sách thống kê cho từng pattern
            List<Map<String, Object>> patternStats = new ArrayList<>();

            for (Pattern pattern : allPatterns) {
                Map<String, Object> stats = new HashMap<>();

                // Thông tin cơ bản của pattern
                stats.put("id", pattern.getId());
                stats.put("name", pattern.getName());
                stats.put("description", pattern.getDescription());
                stats.put("label", pattern.getLabel());
                stats.put("imageBase64", pattern.getImageBase64());
                stats.put("createdAt", pattern.getCreatedAt());
                stats.put("updatedAt", pattern.getUpdatedAt());

                // Thống kê downloads (không hiển thị nhưng vẫn tính để tham khảo)
                int downloadCount = downloadDao.getDownloadCountByPattern(pattern.getId());
                stats.put("downloadCount", downloadCount);

                // Thống kê evaluations (lượt sử dụng = số lượt đánh giá)
                int usageCount = evaluationDao.getEvaluationCountByPattern(pattern.getId()); // Lượt sử dụng
                int successCount = evaluationDao.getSuccessEvaluationCountByPattern(pattern.getId());
                int failureCount = usageCount - successCount;

                stats.put("usageCount", usageCount); // Lượt sử dụng thay cho evaluationCount
                stats.put("successCount", successCount);
                stats.put("failureCount", failureCount);

                // Tính tỷ lệ thành công và thất bại
                double successRate = usageCount > 0 ? (double) successCount / usageCount * 100 : 0;
                double failureRate = usageCount > 0 ? (double) failureCount / usageCount * 100 : 0;

                stats.put("successRate", Math.round(successRate * 100.0) / 100.0); // Làm tròn 2 chữ số thập phân
                stats.put("failureRate", Math.round(failureRate * 100.0) / 100.0); // Tỷ lệ thất bại

                patternStats.add(stats);
            }

            // Sắp xếp theo tiêu chí
            patternStats.sort((a, b) -> {
                Comparable valueA = getComparableValue(a, sortBy);
                Comparable valueB = getComparableValue(b, sortBy);

                if (valueA == null && valueB == null) return 0;
                if (valueA == null) return 1;
                if (valueB == null) return -1;

                int result = valueA.compareTo(valueB);
                return "desc".equals(sortOrder) ? -result : result;
            });

            // Phân trang
            int totalItems = patternStats.size();
            int totalPages = (int) Math.ceil((double) totalItems / size);
            int startIndex = (page - 1) * size;
            int endIndex = Math.min(startIndex + size, totalItems);

            List<Map<String, Object>> pagedStats = patternStats.subList(startIndex, endIndex);

            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("patterns", pagedStats);
            response.put("pagination", createPaginationInfo(page, size, totalItems, totalPages));

            return ResponseEntity.ok(response);
        } catch (Exception e) {
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "Lỗi khi lấy thống kê patterns: " + e.getMessage());
            return ResponseEntity.status(500).body(response);
        }
    }

    /**
     * Lấy top patterns theo tiêu chí
     */
    @GetMapping("/top-patterns")
    public ResponseEntity<?> getTopPatterns(
            @RequestParam(defaultValue = "downloads") String criteria,
            @RequestParam(defaultValue = "10") int limit) {
        try {
            List<Pattern> allPatterns = patternDao.findAll();
            List<Map<String, Object>> topPatterns = new ArrayList<>();

            for (Pattern pattern : allPatterns) {
                Map<String, Object> stats = new HashMap<>();
                stats.put("id", pattern.getId());
                stats.put("name", pattern.getName());
                stats.put("imageBase64", pattern.getImageBase64());

                switch (criteria) {
                    case "downloads":
                        stats.put("value", downloadDao.getDownloadCountByPattern(pattern.getId()));
                        stats.put("label", "lượt tải");
                        break;
                    case "evaluations":
                        stats.put("value", evaluationDao.getEvaluationCountByPattern(pattern.getId()));
                        stats.put("label", "lượt đánh giá");
                        break;
                    case "success_rate":
                        int evalCount = evaluationDao.getEvaluationCountByPattern(pattern.getId());
                        int successCount = evaluationDao.getSuccessEvaluationCountByPattern(pattern.getId());
                        double successRate = evalCount > 0 ? (double) successCount / evalCount * 100 : 0;
                        stats.put("value", Math.round(successRate * 100.0) / 100.0);
                        stats.put("label", "% thành công");
                        break;
                }

                topPatterns.add(stats);
            }

            // Sắp xếp và lấy top
            topPatterns.sort((a, b) -> {
                Double valueA = ((Number) a.get("value")).doubleValue();
                Double valueB = ((Number) b.get("value")).doubleValue();
                return valueB.compareTo(valueA); // Sắp xếp giảm dần
            });

            List<Map<String, Object>> result = topPatterns.subList(0, Math.min(limit, topPatterns.size()));

            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("topPatterns", result);
            response.put("criteria", criteria);

            return ResponseEntity.ok(response);
        } catch (Exception e) {
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "Lỗi khi lấy top patterns: " + e.getMessage());
            return ResponseEntity.status(500).body(response);
        }
    }

    // Helper methods
    private Comparable getComparableValue(Map<String, Object> item, String key) {
        Object value = item.get(key);
        if (value instanceof Number) {
            return ((Number) value).doubleValue();
        } else if (value instanceof String) {
            return (String) value;
        } else if (value instanceof Date) {
            return (Date) value;
        }
        return null;
    }

    private Map<String, Object> createPaginationInfo(int page, int size, int totalItems, int totalPages) {
        Map<String, Object> pagination = new HashMap<>();
        pagination.put("currentPage", page);
        pagination.put("pageSize", size);
        pagination.put("totalItems", totalItems);
        pagination.put("totalPages", totalPages);
        pagination.put("hasNext", page < totalPages);
        pagination.put("hasPrevious", page > 1);
        return pagination;
    }
}
