-- <PERSON><PERSON>t đơn giản tạo bảng DOWNLOAD
USE pattern_management;

CREATE TABLE IF NOT EXISTS download (
    id INT AUTO_INCREMENT PRIMARY KEY,
    pattern_id INT NOT NULL,
    user_id INT NOT NULL,
    download_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Thêm index
CREATE INDEX idx_pattern_id ON download(pattern_id);
CREATE INDEX idx_user_id ON download(user_id);
CREATE INDEX idx_download_date ON download(download_date);

SELECT 'Download table created!' as message;
