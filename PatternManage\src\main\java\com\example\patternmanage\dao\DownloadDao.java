package com.example.patternmanage.dao;

import com.example.patternmanage.model.Download;
import org.springframework.stereotype.Service;

import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import javax.persistence.TypedQuery;
import javax.transaction.Transactional;
import java.util.List;

/**
 * D<PERSON><PERSON> cho bảng download sử dụng JPA
 */
@Service
public class DownloadDao {

    @PersistenceContext
    private EntityManager entityManager;

    /**
     * Lưu download mới
     */
    @Transactional
    public void addDownload(int patternId, int userId) {
        Download download = new Download(patternId, userId);
        entityManager.persist(download);
    }



    /**
     * Tìm download theo ID
     */
    public Download findById(int id) {
        return entityManager.find(Download.class, id);
    }

    /**
     * L<PERSON>y tất cả downloads
     */
    public List<Download> findAll() {
        String jpql = "SELECT d FROM Download d ORDER BY d.downloadDate DESC";
        return entityManager.createQuery(jpql, Download.class).getResultList();
    }

    /**
     * Kiểm tra user đã tải pattern này chưa
     */
    public boolean hasUserDownloaded(int patternId, int userId) {
        String jpql = "SELECT COUNT(d) FROM Download d WHERE d.patternId = :patternId AND d.userId = :userId";
        Long count = entityManager.createQuery(jpql, Long.class)
                .setParameter("patternId", patternId)
                .setParameter("userId", userId)
                .getSingleResult();
        return count > 0;
    }

    /**
     * Lấy số lần download của một pattern
     */
    public int getDownloadCount(int patternId) {
        String jpql = "SELECT COUNT(d) FROM Download d WHERE d.patternId = :patternId";
        Long count = entityManager.createQuery(jpql, Long.class)
                .setParameter("patternId", patternId)
                .getSingleResult();
        return count.intValue();
    }

    /**
     * Lấy danh sách download của một user
     */
    public List<Download> getDownloadsByUser(int userId) {
        String jpql = "SELECT d FROM Download d WHERE d.userId = :userId ORDER BY d.downloadDate DESC";
        return entityManager.createQuery(jpql, Download.class)
                .setParameter("userId", userId)
                .getResultList();
    }

    /**
     * Lấy danh sách download của một pattern
     */
    public List<Download> getDownloadsByPattern(int patternId) {
        String jpql = "SELECT d FROM Download d WHERE d.patternId = :patternId ORDER BY d.downloadDate DESC";
        return entityManager.createQuery(jpql, Download.class)
                .setParameter("patternId", patternId)
                .getResultList();
    }

    /**
     * Lấy thống kê download
     */
    public int getTotalDownloads() {
        String jpql = "SELECT COUNT(d) FROM Download d";
        Long count = entityManager.createQuery(jpql, Long.class).getSingleResult();
        return count.intValue();
    }

    /**
     * Đếm số lần download của một pattern cụ thể
     */
    public int getDownloadCountByPattern(int patternId) {
        String jpql = "SELECT COUNT(d) FROM Download d WHERE d.patternId = :patternId";
        Long count = entityManager.createQuery(jpql, Long.class)
                .setParameter("patternId", patternId)
                .getSingleResult();
        return count.intValue();
    }

    /**
     * Lấy top patterns được download nhiều nhất
     */
    public List<Object[]> getTopDownloadedPatterns(int limit) {
        String jpql = "SELECT d.patternId, COUNT(d) as downloadCount " +
                     "FROM Download d " +
                     "GROUP BY d.patternId " +
                     "ORDER BY downloadCount DESC";
        TypedQuery<Object[]> query = entityManager.createQuery(jpql, Object[].class);
        query.setMaxResults(limit);
        return query.getResultList();
    }

    /**
     * Xóa download theo ID
     */
    @Transactional
    public void deleteDownload(int id) {
        Download download = findById(id);
        if (download != null) {
            entityManager.remove(download);
        }
    }

    /**
     * Xóa download theo ID (alias cho deleteDownload)
     */
    @Transactional
    public void delete(int id) {
        deleteDownload(id);
    }

    /**
     * Xóa download của user cho pattern cụ thể
     */
    @Transactional
    public void removeDownload(int patternId, int userId) {
        String jpql = "DELETE FROM Download d WHERE d.patternId = :patternId AND d.userId = :userId";
        entityManager.createQuery(jpql)
                .setParameter("patternId", patternId)
                .setParameter("userId", userId)
                .executeUpdate();
    }

    /**
     * Đếm downloads của user theo thời gian
     */
    public long countByUserId(int userId) {
        String jpql = "SELECT COUNT(d) FROM Download d WHERE d.userId = :userId";
        return entityManager.createQuery(jpql, Long.class)
                .setParameter("userId", userId)
                .getSingleResult();
    }


}
