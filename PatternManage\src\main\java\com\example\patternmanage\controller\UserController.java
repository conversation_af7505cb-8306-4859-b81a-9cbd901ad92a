package com.example.patternmanage.controller;

import com.example.patternmanage.dao.UserDao;
import com.example.patternmanage.model.User;
import com.example.patternmanage.model.UserRole;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@CrossOrigin(origins = "*")
@Controller
@RequestMapping("/users")
public class UserController {

    @Autowired
    private UserDao userDao;

    /**
     * Tạo response user (không bao gồm password)
     */
    private Map<String, Object> createUserResponse(User user) {
        Map<String, Object> userInfo = new HashMap<>();
        userInfo.put("id", user.getId());
        userInfo.put("username", user.getUsername());
        userInfo.put("email", user.getEmail());
        userInfo.put("name", user.getName());
        userInfo.put("phoneNumber", user.getPhoneNumber());
        userInfo.put("role", user.getRole().name());
        userInfo.put("roleDisplayName", user.getRole().getDisplayName());
        userInfo.put("isAdmin", user.getRole().isAdmin());
        userInfo.put("isActive", user.isActive());
        userInfo.put("createdAt", user.getCreatedAt());
        userInfo.put("updatedAt", user.getUpdatedAt());
        return userInfo;
    }

    /**
     * Lấy danh sách tất cả người dùng (CHỈ ADMIN)
     */
    @GetMapping("/all/{adminId}")
    public ResponseEntity<?> getAllUsers(@PathVariable int adminId) {
        try {
            // Kiểm tra quyền admin
            if (!userDao.isAdmin(adminId)) {
                Map<String, Object> result = new HashMap<>();
                result.put("success", false);
                result.put("message", "Bạn không có quyền xem danh sách người dùng");
                return ResponseEntity.status(HttpStatus.FORBIDDEN).body(result);
            }

            List<User> users = userDao.findAll();
            List<Map<String, Object>> userList = new ArrayList<>();
            
            for (User user : users) {
                userList.add(createUserResponse(user));
            }

            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("users", userList);
            result.put("total", userList.size());
            
            return ResponseEntity.ok(result);
            
        } catch (Exception e) {
            Map<String, Object> result = new HashMap<>();
            result.put("success", false);
            result.put("message", "Lỗi khi lấy danh sách người dùng: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(result);
        }
    }

    /**
     * Lấy thông tin người dùng theo ID
     */
    @GetMapping("/{id}")
    public ResponseEntity<?> getUserById(@PathVariable int id,
                                       @RequestParam("requesterId") int requesterId) {
        try {
            // Kiểm tra quyền: admin hoặc chính user đó
            User requester = userDao.findById(requesterId);
            if (requester == null) {
                return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body("Người dùng không tồn tại");
            }

            if (!requester.getRole().isAdmin() && requesterId != id) {
                Map<String, Object> result = new HashMap<>();
                result.put("success", false);
                result.put("message", "Bạn không có quyền xem thông tin người dùng này");
                return ResponseEntity.status(HttpStatus.FORBIDDEN).body(result);
            }

            User user = userDao.findById(id);
            Map<String, Object> result = new HashMap<>();
            
            if (user == null) {
                result.put("success", false);
                result.put("message", "Không tìm thấy người dùng");
                return ResponseEntity.status(HttpStatus.NOT_FOUND).body(result);
            }

            result.put("success", true);
            result.put("user", createUserResponse(user));
            return ResponseEntity.ok(result);
            
        } catch (Exception e) {
            Map<String, Object> result = new HashMap<>();
            result.put("success", false);
            result.put("message", "Lỗi khi lấy thông tin người dùng: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(result);
        }
    }

    /**
     * Thêm người dùng mới (CHỈ ADMIN)
     */
    @PostMapping("/add/{adminId}")
    public ResponseEntity<?> addUser(@PathVariable int adminId,
                                   @RequestParam("username") String username,
                                   @RequestParam("email") String email,
                                   @RequestParam("password") String password,
                                   @RequestParam(value = "name", required = false) String name,
                                   @RequestParam(value = "phoneNumber", required = false) String phoneNumber,
                                   @RequestParam(value = "role", defaultValue = "USER") String role) {
        
        // Validate input
        if (username == null || username.trim().isEmpty()) {
            return ResponseEntity.badRequest().body("Tên người dùng không được để trống");
        }
        if (email == null || email.trim().isEmpty()) {
            return ResponseEntity.badRequest().body("Email không được để trống");
        }
        if (password == null || password.length() < 6) {
            return ResponseEntity.badRequest().body("Mật khẩu phải có ít nhất 6 ký tự");
        }

        try {
            // Kiểm tra quyền admin
            if (!userDao.isAdmin(adminId)) {
                Map<String, Object> result = new HashMap<>();
                result.put("success", false);
                result.put("message", "Bạn không có quyền thêm người dùng");
                return ResponseEntity.status(HttpStatus.FORBIDDEN).body(result);
            }

            // Kiểm tra username hoặc email đã tồn tại
            if (userDao.existsByUsernameOrEmail(username.trim(), email.trim())) {
                Map<String, Object> result = new HashMap<>();
                result.put("success", false);
                result.put("message", "Tên người dùng hoặc email đã tồn tại");
                return ResponseEntity.status(HttpStatus.CONFLICT).body(result);
            }

            // Parse role
            UserRole userRole = UserRole.fromString(role);

            // Tạo user mới
            userDao.saveOrUpdate(null, username.trim(), email.trim(), password, name, phoneNumber, userRole);
            
            // Lấy thông tin user vừa tạo
            User newUser = userDao.findByUsername(username.trim());
            
            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("message", "Thêm người dùng thành công");
            result.put("user", createUserResponse(newUser));
            
            return ResponseEntity.ok(result);
            
        } catch (Exception e) {
            Map<String, Object> result = new HashMap<>();
            result.put("success", false);
            result.put("message", "Lỗi khi thêm người dùng: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(result);
        }
    }

    /**
     * Cập nhật thông tin người dùng
     */
    @PostMapping("/update/{id}")
    public ResponseEntity<?> updateUser(@PathVariable int id,
                                      @RequestParam("requesterId") int requesterId,
                                      @RequestParam("username") String username,
                                      @RequestParam("email") String email,
                                      @RequestParam(value = "password", required = false) String password,
                                      @RequestParam(value = "name", required = false) String name,
                                      @RequestParam(value = "phoneNumber", required = false) String phoneNumber,
                                      @RequestParam(value = "role", required = false) String role) {
        
        // Validate input
        if (username == null || username.trim().isEmpty()) {
            return ResponseEntity.badRequest().body("Tên người dùng không được để trống");
        }
        if (email == null || email.trim().isEmpty()) {
            return ResponseEntity.badRequest().body("Email không được để trống");
        }

        try {
            // Kiểm tra quyền: admin hoặc chính user đó
            User requester = userDao.findById(requesterId);
            if (requester == null) {
                return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body("Người dùng không tồn tại");
            }

            boolean isAdmin = requester.getRole().isAdmin();
            if (!isAdmin && requesterId != id) {
                Map<String, Object> result = new HashMap<>();
                result.put("success", false);
                result.put("message", "Bạn không có quyền cập nhật thông tin người dùng này");
                return ResponseEntity.status(HttpStatus.FORBIDDEN).body(result);
            }

            // Lấy thông tin user hiện tại
            User currentUser = userDao.findById(id);
            if (currentUser == null) {
                Map<String, Object> result = new HashMap<>();
                result.put("success", false);
                result.put("message", "Không tìm thấy người dùng");
                return ResponseEntity.status(HttpStatus.NOT_FOUND).body(result);
            }

            // Kiểm tra username hoặc email đã tồn tại (loại trừ user hiện tại)
            if (userDao.existsByUsernameOrEmailExcludingId(username.trim(), email.trim(), id)) {
                Map<String, Object> result = new HashMap<>();
                result.put("success", false);
                result.put("message", "Tên người dùng hoặc email đã tồn tại");
                return ResponseEntity.status(HttpStatus.CONFLICT).body(result);
            }

            // Xử lý password
            String finalPassword = (password != null && !password.isEmpty()) ? password : currentUser.getPassword();

            // Xử lý role (chỉ admin mới được thay đổi role)
            UserRole finalRole = currentUser.getRole();
            if (isAdmin && role != null && !role.isEmpty()) {
                finalRole = UserRole.fromString(role);
            }

            // Cập nhật user
            userDao.saveOrUpdate(id, username.trim(), email.trim(), finalPassword, name, phoneNumber, finalRole);
            
            // Lấy thông tin user sau khi cập nhật
            User updatedUser = userDao.findById(id);
            
            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("message", "Cập nhật thông tin thành công");
            result.put("user", createUserResponse(updatedUser));
            
            return ResponseEntity.ok(result);
            
        } catch (Exception e) {
            Map<String, Object> result = new HashMap<>();
            result.put("success", false);
            result.put("message", "Lỗi khi cập nhật người dùng: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(result);
        }
    }

    /**
     * Xóa người dùng (CHỈ ADMIN)
     */
    @DeleteMapping("/delete/{id}/{adminId}")
    public ResponseEntity<?> deleteUser(@PathVariable int id, @PathVariable int adminId) {
        try {
            // Kiểm tra quyền admin
            if (!userDao.isAdmin(adminId)) {
                Map<String, Object> result = new HashMap<>();
                result.put("success", false);
                result.put("message", "Bạn không có quyền xóa người dùng");
                return ResponseEntity.status(HttpStatus.FORBIDDEN).body(result);
            }

            // Không cho phép admin xóa chính mình
            if (id == adminId) {
                Map<String, Object> result = new HashMap<>();
                result.put("success", false);
                result.put("message", "Bạn không thể xóa chính mình");
                return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(result);
            }

            // Kiểm tra user tồn tại
            User user = userDao.findById(id);
            if (user == null) {
                Map<String, Object> result = new HashMap<>();
                result.put("success", false);
                result.put("message", "Không tìm thấy người dùng");
                return ResponseEntity.status(HttpStatus.NOT_FOUND).body(result);
            }

            // Kiểm tra nếu là admin cuối cùng
            if (user.getRole().isAdmin() && userDao.countAdmins() <= 1) {
                Map<String, Object> result = new HashMap<>();
                result.put("success", false);
                result.put("message", "Không thể xóa admin cuối cùng trong hệ thống");
                return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(result);
            }

            // Xóa user
            userDao.deleteById(id);
            
            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("message", "Xóa người dùng thành công");
            
            return ResponseEntity.ok(result);
            
        } catch (Exception e) {
            Map<String, Object> result = new HashMap<>();
            result.put("success", false);
            result.put("message", "Lỗi khi xóa người dùng: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(result);
        }
    }

    /**
     * Tìm kiếm người dùng (CHỈ ADMIN)
     */
    @GetMapping("/search/{adminId}")
    public ResponseEntity<?> searchUsers(@PathVariable int adminId,
                                       @RequestParam("keyword") String keyword) {
        try {
            // Kiểm tra quyền admin
            if (!userDao.isAdmin(adminId)) {
                Map<String, Object> result = new HashMap<>();
                result.put("success", false);
                result.put("message", "Bạn không có quyền tìm kiếm người dùng");
                return ResponseEntity.status(HttpStatus.FORBIDDEN).body(result);
            }

            List<User> users = userDao.searchByKeyword(keyword);
            List<Map<String, Object>> userList = new ArrayList<>();
            
            for (User user : users) {
                userList.add(createUserResponse(user));
            }

            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("users", userList);
            result.put("total", userList.size());
            result.put("keyword", keyword);
            
            return ResponseEntity.ok(result);
            
        } catch (Exception e) {
            Map<String, Object> result = new HashMap<>();
            result.put("success", false);
            result.put("message", "Lỗi khi tìm kiếm người dùng: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(result);
        }
    }

    /**
     * Thống kê người dùng (CHỈ ADMIN)
     */
    @GetMapping("/statistics/{adminId}")
    public ResponseEntity<?> getUserStatistics(@PathVariable int adminId) {
        try {
            // Kiểm tra quyền admin
            if (!userDao.isAdmin(adminId)) {
                Map<String, Object> result = new HashMap<>();
                result.put("success", false);
                result.put("message", "Bạn không có quyền xem thống kê");
                return ResponseEntity.status(HttpStatus.FORBIDDEN).body(result);
            }

            Map<String, Object> statistics = new HashMap<>();
            statistics.put("totalUsers", userDao.countAll());
            statistics.put("totalAdmins", userDao.countAdmins());
            statistics.put("totalRegularUsers", userDao.countUsers());
            statistics.put("activeUsers", userDao.countActiveUsers());

            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("statistics", statistics);
            
            return ResponseEntity.ok(result);
            
        } catch (Exception e) {
            Map<String, Object> result = new HashMap<>();
            result.put("success", false);
            result.put("message", "Lỗi khi lấy thống kê: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(result);
        }
    }

    /**
     * Kích hoạt/vô hiệu hóa người dùng (CHỈ ADMIN)
     */
    @PostMapping("/toggle-status/{id}/{adminId}")
    public ResponseEntity<?> toggleUserStatus(@PathVariable int id, @PathVariable int adminId) {
        try {
            // Kiểm tra quyền admin
            if (!userDao.isAdmin(adminId)) {
                Map<String, Object> result = new HashMap<>();
                result.put("success", false);
                result.put("message", "Bạn không có quyền thay đổi trạng thái người dùng");
                return ResponseEntity.status(HttpStatus.FORBIDDEN).body(result);
            }

            // Không cho phép admin vô hiệu hóa chính mình
            if (id == adminId) {
                Map<String, Object> result = new HashMap<>();
                result.put("success", false);
                result.put("message", "Bạn không thể thay đổi trạng thái của chính mình");
                return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(result);
            }

            User user = userDao.findById(id);
            if (user == null) {
                Map<String, Object> result = new HashMap<>();
                result.put("success", false);
                result.put("message", "Không tìm thấy người dùng");
                return ResponseEntity.status(HttpStatus.NOT_FOUND).body(result);
            }

            // Toggle trạng thái
            boolean newStatus = !user.isActive();
            userDao.updateUserStatus(id, newStatus);

            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("message", newStatus ? "Kích hoạt người dùng thành công" : "Vô hiệu hóa người dùng thành công");
            result.put("newStatus", newStatus);
            
            return ResponseEntity.ok(result);
            
        } catch (Exception e) {
            Map<String, Object> result = new HashMap<>();
            result.put("success", false);
            result.put("message", "Lỗi khi thay đổi trạng thái: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(result);
        }
    }
}
