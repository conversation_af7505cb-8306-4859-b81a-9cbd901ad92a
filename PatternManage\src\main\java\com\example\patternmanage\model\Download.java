package com.example.patternmanage.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import javax.persistence.*;
import java.time.LocalDateTime;

/**
 * Model cho bảng download
 * Lưu trữ lịch sử tải mẫu của người dùng
 */
@Entity
@Table(name = "downloads")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class Download {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private int id;

    @Column(name = "pattern_id", nullable = false)
    private int patternId;

    @Column(name = "user_id", nullable = false)
    private int userId;

    @Column(name = "download_date", nullable = false)
    private LocalDateTime downloadDate;

    @CreationTimestamp
    @Column(name = "created_at", updatable = false)
    private LocalDateTime createdAt;

    @UpdateTimestamp
    @Column(name = "updated_at")
    private LocalDateTime updatedAt;

    // Thông tin bổ sung từ join với bảng khác (không map vào database)
    @Transient
    private String patternName;

    @Transient
    private String username;

    @Transient
    private String userEmail;

    // Constructor tiện ích cho việc tạo download mới
    public Download(int patternId, int userId) {
        this.patternId = patternId;
        this.userId = userId;
        this.downloadDate = LocalDateTime.now();
    }
}
