package com.example.patternmanage.dao;

import com.example.patternmanage.model.Evaluation;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import javax.persistence.TypedQuery;
import java.time.LocalDateTime;
import java.util.List;

/**
 * DAO cho bảng evaluation sử dụng JPA
 */
@Service
public class EvaluationDao {

    @PersistenceContext
    private EntityManager entityManager;

    /**
     * Lưu evaluation mới
     */
    @Transactional
    public void addEvaluation(int userId, int patternId, String result) {
        Evaluation evaluation = new Evaluation(userId, patternId, result);
        entityManager.persist(evaluation);
    }



    /**
     * Tìm evaluation theo ID
     */
    public Evaluation findById(int id) {
        return entityManager.find(Evaluation.class, id);
    }

    /**
     * Lấy tất cả evaluations
     */
    public List<Evaluation> findAll() {
        String jpql = "SELECT e FROM Evaluation e ORDER BY e.evaluationDate DESC";
        return entityManager.createQuery(jpql, Evaluation.class).getResultList();
    }

    /**
     * Lấy evaluation của user cho pattern cụ thể
     */
    public Evaluation getUserEvaluation(int userId, int patternId) {
        String jpql = "SELECT e FROM Evaluation e WHERE e.userId = :userId AND e.patternId = :patternId";
        TypedQuery<Evaluation> query = entityManager.createQuery(jpql, Evaluation.class)
                .setParameter("userId", userId)
                .setParameter("patternId", patternId);

        List<Evaluation> results = query.getResultList();
        return results.isEmpty() ? null : results.get(0);
    }

    /**
     * Lấy số lượng evaluations của một pattern
     */
    public int getEvaluationCount(int patternId) {
        String jpql = "SELECT COUNT(e) FROM Evaluation e WHERE e.patternId = :patternId";
        Long count = entityManager.createQuery(jpql, Long.class)
                .setParameter("patternId", patternId)
                .getSingleResult();
        return count.intValue();
    }

    /**
     * Alias cho getEvaluationCount để phù hợp với StatisticsController
     */
    public int getEvaluationCountByPattern(int patternId) {
        return getEvaluationCount(patternId);
    }

    /**
     * Lấy số lượng evaluations thành công của một pattern
     */
    public int getSuccessCount(int patternId) {
        String jpql = "SELECT COUNT(e) FROM Evaluation e WHERE e.patternId = :patternId AND e.description = :result";
        Long count = entityManager.createQuery(jpql, Long.class)
                .setParameter("patternId", patternId)
                .setParameter("result", "Thành công")
                .getSingleResult();
        return count.intValue();
    }

    /**
     * Alias cho getSuccessCount để phù hợp với StatisticsController
     */
    public int getSuccessEvaluationCountByPattern(int patternId) {
        return getSuccessCount(patternId);
    }

    /**
     * Lấy số lượng evaluations thất bại của một pattern
     */
    public int getFailedCount(int patternId) {
        String jpql = "SELECT COUNT(e) FROM Evaluation e WHERE e.patternId = :patternId AND " +
                     "(e.description = :result1 OR e.description = :result2)";
        Long count = entityManager.createQuery(jpql, Long.class)
                .setParameter("patternId", patternId)
                .setParameter("result1", "Không thành công")
                .setParameter("result2", "KHÔNG THÀNH CÔNG")
                .getSingleResult();
        return count.intValue();
    }

    /**
     * Tính tỷ lệ thành công của một pattern (%)
     */
    public double getSuccessRate(int patternId) {
        int total = getEvaluationCount(patternId);
        if (total == 0) return 0.0;

        int success = getSuccessCount(patternId);
        return (double) success / total * 100.0;
    }

    /**
     * Lấy danh sách evaluations của một user
     */
    public List<Evaluation> getEvaluationsByUser(int userId) {
        String jpql = "SELECT e FROM Evaluation e WHERE e.userId = :userId ORDER BY e.evaluationDate DESC";
        return entityManager.createQuery(jpql, Evaluation.class)
                .setParameter("userId", userId)
                .getResultList();
    }

    /**
     * Lấy danh sách evaluations của một pattern
     */
    public List<Evaluation> getEvaluationsByPattern(int patternId) {
        String jpql = "SELECT e FROM Evaluation e WHERE e.patternId = :patternId ORDER BY e.evaluationDate DESC";
        return entityManager.createQuery(jpql, Evaluation.class)
                .setParameter("patternId", patternId)
                .getResultList();
    }

    /**
     * Lấy thống kê tổng quan
     */
    public int getTotalEvaluations() {
        String jpql = "SELECT COUNT(e) FROM Evaluation e";
        Long count = entityManager.createQuery(jpql, Long.class).getSingleResult();
        return count.intValue();
    }

    /**
     * Lấy tổng số evaluations thành công
     */
    public int getTotalSuccessEvaluations() {
        String jpql = "SELECT COUNT(e) FROM Evaluation e WHERE e.description = :result";
        Long count = entityManager.createQuery(jpql, Long.class)
                .setParameter("result", "Thành công")
                .getSingleResult();
        return count.intValue();
    }

    /**
     * Lấy tổng số evaluations thất bại
     */
    public int getTotalFailedEvaluations() {
        String jpql = "SELECT COUNT(e) FROM Evaluation e WHERE " +
                     "(e.description = :result1 OR e.description = :result2)";
        Long count = entityManager.createQuery(jpql, Long.class)
                .setParameter("result1", "Không thành công")
                .setParameter("result2", "KHÔNG THÀNH CÔNG")
                .getSingleResult();
        return count.intValue();
    }

    /**
     * Alias methods cho StatisticsController
     */
    public int getTotalSuccessCount() {
        return getTotalSuccessEvaluations();
    }

    public int getTotalFailureCount() {
        return getTotalFailedEvaluations();
    }

    /**
     * Tính tỷ lệ thành công tổng thể (%)
     */
    public double getOverallSuccessRate() {
        int total = getTotalEvaluations();
        if (total == 0) return 0.0;

        int success = getTotalSuccessEvaluations();
        return (double) success / total * 100.0;
    }

    /**
     * Lấy top patterns có tỷ lệ thành công cao nhất
     */
    public List<Object[]> getTopSuccessfulPatterns(int limit) {
        String jpql = "SELECT e.patternId, " +
                     "COUNT(e) as totalEvaluations, " +
                     "COUNT(CASE WHEN e.description = :success THEN 1 END) as successCount, " +
                     "(COUNT(CASE WHEN e.description = :success THEN 1 END) * 100.0 / COUNT(e)) as successRate " +
                     "FROM Evaluation e " +
                     "GROUP BY e.patternId " +
                     "HAVING COUNT(e) > 0 " +
                     "ORDER BY successRate DESC, totalEvaluations DESC";

        TypedQuery<Object[]> query = entityManager.createQuery(jpql, Object[].class);
        query.setParameter("success", "Thành công");
        query.setMaxResults(limit);
        return query.getResultList();
    }

    /**
     * Xóa evaluation theo ID
     */
    @Transactional
    public void deleteEvaluation(int id) {
        Evaluation evaluation = findById(id);
        if (evaluation != null) {
            entityManager.remove(evaluation);
        }
    }

    /**
     * Đếm evaluations của user theo thời gian
     */
    public long countByUserId(int userId) {
        String jpql = "SELECT COUNT(e) FROM Evaluation e WHERE e.userId = :userId";
        return entityManager.createQuery(jpql, Long.class)
                .setParameter("userId", userId)
                .getSingleResult();
    }

    /**
     * Đếm evaluations hôm nay của user
     */
    public long countTodayEvaluationsByUserId(int userId) {
        String jpql = "SELECT COUNT(e) FROM Evaluation e WHERE e.userId = :userId AND DATE(e.evaluationDate) = CURRENT_DATE";
        return entityManager.createQuery(jpql, Long.class)
                .setParameter("userId", userId)
                .getSingleResult();
    }

    /**
     * Đếm evaluations tuần này của user
     */
    public long countWeekEvaluationsByUserId(int userId) {
        String jpql = "SELECT COUNT(e) FROM Evaluation e WHERE e.userId = :userId AND e.evaluationDate >= :weekStart";
        LocalDateTime weekStart = LocalDateTime.now().minusDays(7);
        return entityManager.createQuery(jpql, Long.class)
                .setParameter("userId", userId)
                .setParameter("weekStart", weekStart)
                .getSingleResult();
    }

    /**
     * Đếm evaluations tháng này của user
     */
    public long countMonthEvaluationsByUserId(int userId) {
        String jpql = "SELECT COUNT(e) FROM Evaluation e WHERE e.userId = :userId AND e.evaluationDate >= :monthStart";
        LocalDateTime monthStart = LocalDateTime.now().minusDays(30);
        return entityManager.createQuery(jpql, Long.class)
                .setParameter("userId", userId)
                .setParameter("monthStart", monthStart)
                .getSingleResult();
    }
}
