-- Tạo bảng evaluations mới (cho phép đánh gi<PERSON> nhiề<PERSON> lần)
USE patternmanage;

-- <PERSON><PERSON><PERSON> bảng cũ nếu có (cẩn thận với dữ liệu!)
-- DROP TABLE IF EXISTS evaluation;
-- DROP TABLE IF EXISTS evaluations;

-- T<PERSON><PERSON> bảng evaluations mới
CREATE TABLE IF NOT EXISTS evaluations (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    pattern_id INT NOT NULL,
    description VARCHAR(50) NOT NULL CHECK (description IN ('Thành công', 'Không thành công')),
    evaluation_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    -- Kh<PERSON>a ngoại tham chiếu đến bảng users
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,

    -- <PERSON><PERSON><PERSON><PERSON> ngo<PERSON>i tham chiếu đến bảng patterns
    FOREIGN KEY (pattern_id) REFERENCES patterns(id) ON DELETE CASCADE,

    -- Index để tối ưu truy vấn (KHÔNG có unique constraint)
    INDEX idx_user_id (user_id),
    INDEX idx_pattern_id (pattern_id),
    INDEX idx_evaluation_date (evaluation_date),
    INDEX idx_description (description),
    INDEX idx_user_pattern (user_id, pattern_id)  -- Index bình thường, không unique
);

-- Kiểm tra bảng đã tạo
DESCRIBE evaluations;

-- Xem các index
SHOW INDEX FROM evaluations;
