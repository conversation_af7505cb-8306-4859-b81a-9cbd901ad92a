package com.example.patternmanage.model;

/**
 * Enum định nghĩa các vai trò người dùng trong hệ thống
 */
public enum UserRole {
    /**
     * Quản trị viên - có quyền quản lý toàn bộ hệ thống
     * - Quản lý người dùng (thêm, sửa, xóa, xem)
     * - Quản lý mẫu (thêm, sửa, xóa, xem)
     * - Xem tất cả thống kê và báo cáo
     * - Quản lý đánh giá và lượt tải
     */
    ADMIN("Quản trị viên"),
    
    /**
     * Người dùng thường - chỉ có quyền hạn cơ bản
     * - Xem danh sách mẫu
     * - Tải mẫu
     * - <PERSON><PERSON><PERSON> gi<PERSON> mẫu đã tải
     * - Xem lịch sử tải và đánh giá của bản thân
     */
    USER("Người dùng");

    private final String displayName;

    UserRole(String displayName) {
        this.displayName = displayName;
    }

    /**
     * Lấy tên hiển thị của vai trò
     */
    public String getDisplayName() {
        return displayName;
    }

    /**
     * Kiểm tra có phải là admin không
     */
    public boolean isAdmin() {
        return this == ADMIN;
    }

    /**
     * Kiểm tra có phải là user thường không
     */
    public boolean isUser() {
        return this == USER;
    }

    /**
     * Chuyển đổi từ string sang UserRole
     */
    public static UserRole fromString(String role) {
        if (role == null) {
            return USER; // Mặc định là USER
        }
        
        try {
            return UserRole.valueOf(role.toUpperCase());
        } catch (IllegalArgumentException e) {
            return USER; // Nếu không hợp lệ thì trả về USER
        }
    }

    @Override
    public String toString() {
        return this.name();
    }
}
