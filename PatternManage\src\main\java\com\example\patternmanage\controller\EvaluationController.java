package com.example.patternmanage.controller;

import com.example.patternmanage.dao.EvaluationDao;
import com.example.patternmanage.dao.PatternDao;
import com.example.patternmanage.dao.UserDao;
import com.example.patternmanage.model.Evaluation;
import com.example.patternmanage.model.Pattern;
import com.example.patternmanage.model.User;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/evaluations")
@CrossOrigin(origins = "*")
public class EvaluationController {

    @Autowired
    private EvaluationDao evaluationDao;

    @Autowired
    private UserDao userDao;

    @Autowired
    private PatternDao patternDao;

    @PostMapping("/add")
    public ResponseEntity<?> addOrUpdateEvaluation(@RequestParam("userId") int userId,
                                                   @RequestParam("patternId") int patternId,
                                                   @RequestParam("result") String result) {
        try {
            Map<String, Object> response = new HashMap<>();

            User user = userDao.findById(userId);
            if (user == null) {
                response.put("success", false);
                response.put("message", "Người dùng không tồn tại");
                return ResponseEntity.status(HttpStatus.NOT_FOUND).body(response);
            }

            Pattern pattern = patternDao.findById(patternId);
            if (pattern == null) {
                response.put("success", false);
                response.put("message", "Mẫu không tồn tại");
                return ResponseEntity.status(HttpStatus.NOT_FOUND).body(response);
            }

            if (!"Thành công".equals(result) && !"Không thành công".equals(result)) {
                response.put("success", false);
                response.put("message", "Kết quả đánh giá không hợp lệ");
                return ResponseEntity.badRequest().body(response);
            }

            evaluationDao.addEvaluation(userId, patternId, result);
            response.put("success", true);
            response.put("message", "Thêm đánh giá thành công");
            response.put("action", "created");
            response.put("patternStats", getPatternStats(patternId));

            return ResponseEntity.ok(response);
        } catch (Exception e) {
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "Lỗi khi thêm đánh giá: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }

    @GetMapping("/user/{userId}/pattern/{patternId}")
    public ResponseEntity<?> getUserEvaluation(@PathVariable int userId, @PathVariable int patternId) {
        try {
            Evaluation evaluation = evaluationDao.getUserEvaluation(userId, patternId);

            Map<String, Object> response = new HashMap<>();
            response.put("success", true);

            if (evaluation != null) {
                response.put("hasEvaluated", true);
                response.put("evaluation", createEvaluationResponse(evaluation));
            } else {
                response.put("hasEvaluated", false);
                response.put("evaluation", null);
            }

            return ResponseEntity.ok(response);
        } catch (Exception e) {
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "Lỗi khi lấy đánh giá: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }

    @GetMapping("/user/{userId}")
    public ResponseEntity<?> getUserEvaluations(@PathVariable int userId,
                                              @RequestParam(defaultValue = "1") int page,
                                              @RequestParam(defaultValue = "12") int size) {
        try {

            User user = userDao.findById(userId);
            if (user == null) {
                Map<String, Object> response = new HashMap<>();
                response.put("success", false);
                response.put("message", "Người dùng không tồn tại");
                return ResponseEntity.status(HttpStatus.NOT_FOUND).body(response);
            }

            List<Evaluation> allEvaluations = evaluationDao.getEvaluationsByUser(userId);


            int totalItems = allEvaluations.size();
            int totalPages = (int) Math.ceil((double) totalItems / size);
            int startIndex = (page - 1) * size;
            int endIndex = Math.min(startIndex + size, totalItems);

            List<Evaluation> pagedEvaluations = allEvaluations.subList(startIndex, endIndex);

            List<Map<String, Object>> evaluationList = new ArrayList<>();
            for (Evaluation evaluation : pagedEvaluations) {
                Pattern pattern = patternDao.findById(evaluation.getPatternId());
                if (pattern != null) {
                    Map<String, Object> evalData = createEvaluationResponse(evaluation);
                    evalData.put("patternName", pattern.getName());
                    evalData.put("patternImage", pattern.getImageBase64());
                    evalData.put("patternDescription", pattern.getDescription());
                    evaluationList.add(evalData);
                }
            }

            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("evaluations", evaluationList);
            response.put("pagination", createPaginationInfo(page, size, totalItems, totalPages));
            response.put("userStats", getUserStats(userId));

            return ResponseEntity.ok(response);
        } catch (Exception e) {
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "Lỗi khi lấy danh sách đánh giá: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }

    @GetMapping("/pattern/{patternId}")
    public ResponseEntity<?> getPatternEvaluations(@PathVariable int patternId,
                                                 @RequestParam(defaultValue = "1") int page,
                                                 @RequestParam(defaultValue = "5") int size) {
        try {

            Pattern pattern = patternDao.findById(patternId);
            if (pattern == null) {
                Map<String, Object> response = new HashMap<>();
                response.put("success", false);
                response.put("message", "Mẫu không tồn tại");
                return ResponseEntity.status(HttpStatus.NOT_FOUND).body(response);
            }

            List<Evaluation> allEvaluations = evaluationDao.getEvaluationsByPattern(patternId);


            int totalItems = allEvaluations.size();
            int totalPages = (int) Math.ceil((double) totalItems / size);
            int startIndex = (page - 1) * size;
            int endIndex = Math.min(startIndex + size, totalItems);

            List<Evaluation> pagedEvaluations = allEvaluations.subList(startIndex, endIndex);

            List<Map<String, Object>> evaluationList = new ArrayList<>();
            for (Evaluation evaluation : pagedEvaluations) {
                User user = userDao.findById(evaluation.getUserId());
                if (user != null) {
                    Map<String, Object> evalData = createEvaluationResponse(evaluation);
                    evalData.put("username", user.getUsername());
                    evalData.put("userEmail", user.getEmail());
                    evalData.put("userFullName", user.getName()); // Thêm tên đầy đủ
                    evaluationList.add(evalData);
                }
            }

            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("evaluations", evaluationList);
            response.put("pagination", createPaginationInfo(page, size, totalItems, totalPages));
            response.put("patternStats", getPatternStats(patternId));
            response.put("patternInfo", createPatternInfo(pattern)); // Thêm thông tin pattern

            return ResponseEntity.ok(response);
        } catch (Exception e) {
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "Lỗi khi lấy danh sách đánh giá: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }

    @GetMapping("/statistics")
    public ResponseEntity<?> getStatistics() {
        try {
            Map<String, Object> stats = new HashMap<>();

            int totalEvaluations = evaluationDao.getTotalEvaluations();
            int successEvaluations = evaluationDao.getTotalSuccessEvaluations();
            int failedEvaluations = evaluationDao.getTotalFailedEvaluations();
            double successRate = evaluationDao.getOverallSuccessRate();

            stats.put("totalEvaluations", totalEvaluations);
            stats.put("successEvaluations", successEvaluations);
            stats.put("failedEvaluations", failedEvaluations);
            stats.put("successRate", Math.round(successRate * 100.0) / 100.0);

            List<Object[]> topPatterns = evaluationDao.getTopSuccessfulPatterns(5);
            List<Map<String, Object>> topPatternsList = new ArrayList<>();
            for (Object[] row : topPatterns) {
                int patternId = (int) row[0];
                Pattern pattern = patternDao.findById(patternId);
                if (pattern != null) {
                    Map<String, Object> patternData = new HashMap<>();
                    patternData.put("patternId", patternId);
                    patternData.put("patternName", pattern.getName());
                    patternData.put("totalEvaluations", row[1]);
                    patternData.put("successCount", row[2]);
                    patternData.put("successRate", Math.round((Double) row[3] * 100.0) / 100.0);
                    topPatternsList.add(patternData);
                }
            }
            stats.put("topSuccessfulPatterns", topPatternsList);

            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("statistics", stats);

            return ResponseEntity.ok(response);
        } catch (Exception e) {
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "Lỗi khi lấy thống kê: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }

    @DeleteMapping("/{evaluationId}")
    public ResponseEntity<?> deleteEvaluation(@PathVariable int evaluationId,
                                            @RequestParam("requestUserId") int requestUserId) {
        try {
            Map<String, Object> response = new HashMap<>();

            Evaluation evaluation = evaluationDao.findById(evaluationId);
            if (evaluation == null) {
                response.put("success", false);
                response.put("message", "Đánh giá không tồn tại");
                return ResponseEntity.status(HttpStatus.NOT_FOUND).body(response);
            }

            User requestUser = userDao.findById(requestUserId);
            if (requestUser == null) {
                response.put("success", false);
                response.put("message", "Người dùng không tồn tại");
                return ResponseEntity.status(HttpStatus.NOT_FOUND).body(response);
            }

            boolean isAdmin = userDao.isAdmin(requestUserId);
            boolean isOwner = evaluation.getUserId() == requestUserId;

            if (!isAdmin && !isOwner) {
                response.put("success", false);
                response.put("message", "Bạn không có quyền xóa đánh giá này");
                return ResponseEntity.status(HttpStatus.FORBIDDEN).body(response);
            }

            evaluationDao.deleteEvaluation(evaluationId);

            response.put("success", true);
            response.put("message", "Xóa đánh giá thành công");
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "Lỗi khi xóa đánh giá: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }

    // Helper methods

    private Map<String, Object> createEvaluationResponse(Evaluation evaluation) {
        Map<String, Object> evalData = new HashMap<>();
        evalData.put("id", evaluation.getId());
        evalData.put("userId", evaluation.getUserId());
        evalData.put("patternId", evaluation.getPatternId());
        evalData.put("description", evaluation.getDescriptionText());
        evalData.put("result", evaluation.getDescription());
        evalData.put("isSuccess", evaluation.isSuccess());
        evalData.put("evaluationDate", evaluation.getEvaluationDate());
        return evalData;
    }

    private Map<String, Object> getPatternStats(int patternId) {
        Map<String, Object> stats = new HashMap<>();
        int totalEvaluations = evaluationDao.getEvaluationCount(patternId);
        int successCount = evaluationDao.getSuccessCount(patternId);
        int failureCount = evaluationDao.getFailedCount(patternId);
        double successRate = evaluationDao.getSuccessRate(patternId);

        stats.put("totalEvaluations", totalEvaluations);
        stats.put("successCount", successCount);
        stats.put("failureCount", failureCount);
        stats.put("successRate", Math.round(successRate * 100.0) / 100.0);
        return stats;
    }

    private Map<String, Object> getUserStats(int userId) {
        Map<String, Object> stats = new HashMap<>();
        stats.put("totalEvaluations", evaluationDao.countByUserId(userId));
        stats.put("todayEvaluations", evaluationDao.countTodayEvaluationsByUserId(userId));
        stats.put("weekEvaluations", evaluationDao.countWeekEvaluationsByUserId(userId));
        stats.put("monthEvaluations", evaluationDao.countMonthEvaluationsByUserId(userId));
        return stats;
    }

    private Map<String, Object> createPaginationInfo(int page, int size, int totalItems, int totalPages) {
        Map<String, Object> pagination = new HashMap<>();
        pagination.put("currentPage", page);
        pagination.put("pageSize", size);
        pagination.put("totalItems", totalItems);
        pagination.put("totalPages", totalPages);
        pagination.put("hasNext", page < totalPages);
        pagination.put("hasPrevious", page > 1);
        return pagination;
    }

    private Map<String, Object> createPatternInfo(Pattern pattern) {
        Map<String, Object> patternInfo = new HashMap<>();
        patternInfo.put("id", pattern.getId());
        patternInfo.put("name", pattern.getName());
        patternInfo.put("description", pattern.getDescription());
        patternInfo.put("label", pattern.getLabel());
        patternInfo.put("imageBase64", pattern.getImageBase64());
        patternInfo.put("createdAt", pattern.getCreatedAt());
        patternInfo.put("updatedAt", pattern.getUpdatedAt());
        return patternInfo;
    }
}
