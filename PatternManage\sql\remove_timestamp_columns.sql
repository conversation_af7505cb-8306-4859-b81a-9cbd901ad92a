-- <PERSON><PERSON>t để loại bỏ các cột created_at và updated_at khỏi bảng download và evaluation
-- V<PERSON> các cột này không cần thiết khi đã có download_date và evaluation_date

USE pattern_management;

-- <PERSON><PERSON><PERSON> tra cấu trúc bảng trước khi thay đổi
SELECT 'Cấu trúc bảng download trước khi thay đổi:' as message;
DESCRIBE download;

SELECT 'Cấu trúc bảng evaluation trước khi thay đổi:' as message;
DESCRIBE evaluation;

-- <PERSON><PERSON><PERSON> c<PERSON><PERSON> created_at và updated_at khỏi bảng download
ALTER TABLE download 
DROP COLUMN IF EXISTS created_at,
DROP COLUMN IF EXISTS updated_at;

-- <PERSON><PERSON><PERSON> c<PERSON>t created_at và updated_at khỏi bảng evaluation  
ALTER TABLE evaluation 
DROP COLUMN IF EXISTS created_at,
DROP COLUMN IF EXISTS updated_at;

-- <PERSON><PERSON><PERSON> tra cấu trúc bảng sau khi thay đổi
SELECT 'Cấu trú<PERSON> bảng download sau khi thay đổi:' as message;
DESCRIBE download;

SELECT 'Cấu trúc bảng evaluation sau khi thay đổi:' as message;
DESCRIBE evaluation;

-- Hiển thị dữ liệu mẫu để kiểm tra
SELECT 'Dữ liệu mẫu bảng download:' as message;
SELECT * FROM download LIMIT 5;

SELECT 'Dữ liệu mẫu bảng evaluation:' as message;
SELECT * FROM evaluation LIMIT 5;

SELECT 'Đã loại bỏ thành công các cột created_at và updated_at!' as status;
