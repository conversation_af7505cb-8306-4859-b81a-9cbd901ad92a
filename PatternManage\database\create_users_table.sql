-- Tạo bảng users với cấu trúc theo yêu cầu
CREATE TABLE IF NOT EXISTS users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) NOT NULL UNIQUE,
    email VARCHAR(100) NOT NULL UNIQUE,
    password VARCHAR(255) NOT NULL,
    name VARCHAR(100),
    phone_number VARCHAR(20),
    role ENUM('ADMIN', 'USER') NOT NULL DEFAULT 'USER',
    is_active BOOLEAN NOT NULL DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_username (username),
    INDEX idx_email (email),
    INDEX idx_role (role),
    INDEX idx_is_active (is_active)
);

-- <PERSON><PERSON><PERSON><PERSON> dữ liệu mẫu
INSERT INTO users (username, email, password, name, phone_number, role, is_active) VALUES
('admin', '<EMAIL>', 'admin123', 'Quản trị viên', '0123456789', 'ADMIN', 1),
('admin2', '<EMAIL>', 'admin123', 'Quản trị viên 2', '0123456788', 'ADMIN', 1),
('user1', '<EMAIL>', 'user123', 'Người dùng 1', '0987654321', 'USER', 1),
('user2', '<EMAIL>', 'user123', 'Người dùng 2', '0912345678', 'USER', 1),
('testuser', '<EMAIL>', 'test123', 'Người dùng thử nghiệm', '0909090909', 'USER', 1);

-- Kiểm tra dữ liệu đã thêm
SELECT * FROM users ORDER BY created_at DESC;
