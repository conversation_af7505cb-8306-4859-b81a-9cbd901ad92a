package com.example.patternmanage.dao;

import com.example.patternmanage.model.User;
import com.example.patternmanage.model.UserRole;
import org.springframework.stereotype.Service;

import javax.persistence.EntityManager;
import javax.persistence.NoResultException;
import javax.persistence.PersistenceContext;
import javax.transaction.Transactional;
import java.time.LocalDateTime;
import java.util.List;

@Service
public class UserDao {

    @PersistenceContext
    private EntityManager entityManager;

    /**
     * Tìm tất cả người dùng
     */
    public List<User> findAll() {
        String jpql = "SELECT u FROM User u ORDER BY u.createdAt DESC";
        return entityManager.createQuery(jpql, User.class)
                .getResultList();
    }

    /**
     * Tìm người dùng theo ID
     */
    public User findById(int id) {
        return entityManager.find(User.class, id);
    }

    /**
     * Tìm người dùng theo username
     */
    public User findByUsername(String username) {
        try {
            String jpql = "SELECT u FROM User u WHERE u.username = :username";
            return entityManager.createQuery(jpql, User.class)
                    .setParameter("username", username)
                    .getSingleResult();
        } catch (NoResultException e) {
            return null;
        }
    }



    /**
     * Kiểm tra username hoặc email đã tồn tại
     */
    public boolean existsByUsernameOrEmail(String username, String email) {
        String jpql = "SELECT COUNT(u) FROM User u WHERE u.username = :username OR u.email = :email";
        Long count = entityManager.createQuery(jpql, Long.class)
                .setParameter("username", username)
                .setParameter("email", email)
                .getSingleResult();
        return count > 0;
    }

    /**
     * Kiểm tra username hoặc email đã tồn tại (loại trừ ID hiện tại)
     */
    public boolean existsByUsernameOrEmailExcludingId(String username, String email, int excludeId) {
        String jpql = "SELECT COUNT(u) FROM User u WHERE (u.username = :username OR u.email = :email) AND u.id <> :excludeId";
        Long count = entityManager.createQuery(jpql, Long.class)
                .setParameter("username", username)
                .setParameter("email", email)
                .setParameter("excludeId", excludeId)
                .getSingleResult();
        return count > 0;
    }

    /**
     * Lưu hoặc cập nhật người dùng
     */
    @Transactional
    public void saveOrUpdate(Integer id, String username, String email, String password,
                           String name, String phoneNumber, UserRole role) {
        User user;
        if (id != null) {
            user = entityManager.find(User.class, id);
            if (user == null) {
                throw new RuntimeException("Không tìm thấy người dùng với ID: " + id);
            }
        } else {
            user = new User();
            user.setCreatedAt(LocalDateTime.now());
        }

        user.setUsername(username);
        user.setEmail(email);
        user.setPassword(password); // Trong thực tế nên mã hóa password
        user.setName(name);
        user.setPhoneNumber(phoneNumber);
        user.setRole(role != null ? role : UserRole.USER); // Mặc định là USER
        user.setActive(true); // Mặc định là active

        if (id == null) {
            entityManager.persist(user);
        } else {
            entityManager.merge(user);
        }
    }



    /**
     * Xóa người dùng theo ID
     */
    @Transactional
    public void deleteById(int id) {
        User user = entityManager.find(User.class, id);
        if (user != null) {
            entityManager.remove(user);
        }
    }

    /**
     * Tìm kiếm người dùng theo từ khóa (username, email hoặc name)
     */
    public List<User> searchByKeyword(String keyword) {
        String jpql = "SELECT u FROM User u WHERE LOWER(u.username) LIKE LOWER(CONCAT('%', :keyword, '%')) " +
                     "OR LOWER(u.email) LIKE LOWER(CONCAT('%', :keyword, '%')) " +
                     "OR LOWER(u.name) LIKE LOWER(CONCAT('%', :keyword, '%'))";
        return entityManager.createQuery(jpql, User.class)
                .setParameter("keyword", keyword)
                .getResultList();
    }

    /**
     * Kiểm tra có phải admin không
     */
    public boolean isAdmin(int userId) {
        User user = findById(userId);
        return user != null && user.getRole() == UserRole.ADMIN;
    }

    /**
     * Kích hoạt/vô hiệu hóa người dùng
     */
    @Transactional
    public void updateUserStatus(int userId, boolean isActive) {
        User user = entityManager.find(User.class, userId);
        if (user == null) {
            throw new RuntimeException("Không tìm thấy người dùng với ID: " + userId);
        }
        user.setActive(isActive);
        entityManager.merge(user);
    }

    /**
     * Đếm số lượng admin
     */
    public long countAdmins() {
        String jpql = "SELECT COUNT(u) FROM User u WHERE u.role = :role";
        return entityManager.createQuery(jpql, Long.class)
                .setParameter("role", UserRole.ADMIN)
                .getSingleResult();
    }

    /**
     * Đếm số lượng user thường
     */
    public long countUsers() {
        String jpql = "SELECT COUNT(u) FROM User u WHERE u.role = :role";
        return entityManager.createQuery(jpql, Long.class)
                .setParameter("role", UserRole.USER)
                .getSingleResult();
    }

    /**
     * Đếm tổng số người dùng
     */
    public long countAll() {
        String jpql = "SELECT COUNT(u) FROM User u";
        return entityManager.createQuery(jpql, Long.class)
                .getSingleResult();
    }

    /**
     * Đếm số người dùng đang hoạt động
     */
    public long countActiveUsers() {
        String jpql = "SELECT COUNT(u) FROM User u WHERE u.isActive = true";
        return entityManager.createQuery(jpql, Long.class)
                .getSingleResult();
    }
}
